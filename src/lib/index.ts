// Main exports
export { WidgetsProvider, useWidgets } from './WidgetsProvider';
export { WidgetsCatalog } from './WidgetsCatalog';
export { Layout } from './Layout';

// Hook exports
export { useLocalStorage, isLocalStorageAvailable, getLocalStorageItem, setLocalStorageItem, removeLocalStorageItem } from './hooks/useLocalStorage';
export { useWidgetState, useGlobalWidgetStates, withWidgetState } from './hooks/useWidgetState';
export { useLayoutPersistence, useAutoSaveLayout, useLayoutPersistenceWithWidgets, serializeWidgetsForStorage, restoreWidgetsFromStorage } from './hooks/useLayoutPersistence';


// Type exports
export type {
  Widget,
  WidgetCategory,
  WidgetsContextType,
  WidgetsProviderProps,
  WidgetsCatalogProps,
  LayoutProps,
  LayoutType,
  DragItem,
  WidgetStateData,
  LayoutStateData,
  PersistenceConfig,
  UseWidgetStateReturn,
  UseLayoutPersistenceReturn,
} from './types';

// Default export for convenience
export { WidgetsProvider as default } from './WidgetsProvider';
