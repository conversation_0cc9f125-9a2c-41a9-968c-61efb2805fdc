import React from 'react';
import { useWidgets } from './WidgetsProvider';
import type { Widget, WidgetsCatalogProps } from './types';

export const WidgetsCatalog: React.FC<WidgetsCatalogProps> = ({
  className = '',
  widgets: propWidgets,
  onWidgetSelect,
  onDrag,
  renderWidget,
  enableDrag = false,
}) => {
  const { widgets: contextWidgets } = useWidgets();
  const widgets = propWidgets || contextWidgets;

  const handleWidgetClick = (widget: Widget) => {
    if (onWidgetSelect) {
      onWidgetSelect(widget);
    }
  };

  const handleDragStart = (widget: Widget, event: React.DragEvent) => {
    if (enableDrag) {
      event.dataTransfer.setData('application/json', JSON.stringify({
        widget,
        type: 'widget'
      }));
      event.dataTransfer.effectAllowed = 'copy';

      if (onDrag) {
        onDrag(widget);
      }
    }
  };

  const defaultRenderWidget = (widget: Widget) => (
    <div
      key={widget.id}
      className="widget-card"
      draggable={enableDrag}
      onClick={() => handleWidgetClick(widget)}
      onDragStart={(e) => handleDragStart(widget, e)}
      style={{
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        padding: '16px',
        margin: '8px',
        cursor: enableDrag ? 'grab' : 'pointer',
        backgroundColor: '#fff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        transition: 'box-shadow 0.2s ease',
        userSelect: 'none',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
      }}
      onDragEnd={(e) => {
        if (enableDrag) {
          e.currentTarget.style.cursor = 'grab';
        }
      }}
    >
      <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', fontWeight: '600' }}>
        {widget.name}
      </h3>
      {widget.description && (
        <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px' }}>
          {widget.description}
        </p>
      )}
      {widget.tags && widget.tags.length > 0 && (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
          {widget.tags.map((tag, index) => (
            <span
              key={index}
              style={{
                backgroundColor: '#f0f0f0',
                padding: '2px 8px',
                borderRadius: '12px',
                fontSize: '12px',
                color: '#666',
              }}
            >
              {tag}
            </span>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className={`widgets-catalog ${className}`} style={{ padding: '16px' }}>
      <div
        className="widgets-grid"
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
          gap: '16px',
        }}
      >
        {widgets.length > 0 ? (
          widgets.map((widget) =>
            renderWidget ? renderWidget(widget) : defaultRenderWidget(widget)
          )
        ) : (
          <div style={{ gridColumn: '1 / -1', textAlign: 'center', color: '#666' }}>
            No widgets found
          </div>
        )}
      </div>
    </div>
  );
};

export default WidgetsCatalog;
