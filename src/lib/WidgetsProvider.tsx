import React, { createContext, useContext, useState, useCallback } from 'react';
import type { Widget, WidgetsContextType, WidgetsProviderProps } from './types';

const WidgetsContext = createContext<WidgetsContextType | undefined>(undefined);

export const useWidgets = (): WidgetsContextType => {
  const context = useContext(WidgetsContext);
  if (!context) {
    throw new Error('useWidgets must be used within a WidgetsProvider');
  }
  return context;
};

export const WidgetsProvider: React.FC<Omit<WidgetsProviderProps, 'initialCategories'>> = ({
  children,
  initialWidgets = [],
}) => {
  const [widgets, setWidgets] = useState<Widget[]>(initialWidgets);

  const addWidget = useCallback((widget: Widget) => {
    setWidgets(prev => {
      // Check if widget already exists
      if (prev.some(w => w.id === widget.id)) {
        console.warn(`Widget with id "${widget.id}" already exists`);
        return prev;
      }
      return [...prev, widget];
    });
  }, []);

  const removeWidget = useCallback((widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  }, []);

  // Simple implementations for compatibility
  const updateWidgetState = useCallback(() => {
    // Widgets now handle their own state
  }, []);

  const getWidgetState = useCallback(() => {
    // Widgets now handle their own state
    return {};
  }, []);

  const clearAllStates = useCallback(() => {
    // Clear all widget states from localStorage
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('widget-')) {
        localStorage.removeItem(key);
      }
    });
  }, []);

  const contextValue: WidgetsContextType = {
    widgets,
    categories: [],
    addWidget,
    removeWidget,
    updateWidgetState,
    getWidgetState,
    clearAllStates,
  };

  return (
    <WidgetsContext.Provider value={contextValue}>
      {children}
    </WidgetsContext.Provider>
  );
};

export default WidgetsProvider;
