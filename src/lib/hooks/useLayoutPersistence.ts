import React, { useCallback, useEffect, useMemo } from 'react';
import { useLocalStorage } from './useLocalStorage';
import type { Layout as RGLLayout } from 'react-grid-layout';
import type { Widget, LayoutStateData, UseLayoutPersistenceReturn } from '../types';

/**
 * Hook for managing layout state persistence
 * Handles saving and restoring both the grid layout and dropped widgets
 */
export function useLayoutPersistence(
  options: {
    enablePersistence?: boolean;
    storageKey?: string;
    autoSave?: boolean;
    saveInterval?: number;
  } = {}
): UseLayoutPersistenceReturn {
  const {
    enablePersistence = true,
    storageKey = 'layout-state',
  } = options;

  // Store layout state in localStorage
  const [layoutState, setLayoutState, clearLayoutState] = useLocalStorage<LayoutStateData | null>(
    storageKey,
    null
  );

  // Check for persisted data using useMemo to prevent infinite loops
  const hasPersistedData = useMemo(() => {
    return layoutState !== null &&
      layoutState?.layout &&
      Array.isArray(layoutState.layout) &&
      layoutState.layout.length > 0;
  }, [layoutState]);

  // Save layout state
  const saveLayoutState = useCallback(
    (layout: RGLLayout[], droppedWidgets: Widget[]) => {
      if (!enablePersistence) {
        return;
      }

      const stateData: LayoutStateData = {
        layout,
        droppedWidgets,
        timestamp: Date.now(),
      };

      setLayoutState(stateData);
    },
    [enablePersistence, setLayoutState]
  );

  // Clear layout state
  const clearLayout = useCallback(() => {
    clearLayoutState();
  }, [clearLayoutState]);

  // Get saved layout and widgets
  const savedLayout = layoutState?.layout || [];
  const savedDroppedWidgets = layoutState?.droppedWidgets || [];

  return {
    savedLayout,
    savedDroppedWidgets,
    saveLayoutState,
    clearLayoutState: clearLayout,
    hasPersistedData,
  };
}

/**
 * Hook for auto-saving layout state with debouncing
 */
export function useAutoSaveLayout(
  layout: RGLLayout[],
  droppedWidgets: Widget[],
  options: {
    enablePersistence?: boolean;
    storageKey?: string;
    saveInterval?: number;
  } = {}
) {
  const {
    enablePersistence = true,
    storageKey = 'layout-state',
    saveInterval = 1000,
  } = options;

  const { saveLayoutState } = useLayoutPersistence({
    enablePersistence,
    storageKey,
  });

  // Debounced save effect
  useEffect(() => {
    if (!enablePersistence || layout.length === 0) {
      return;
    }

    const timeoutId = setTimeout(() => {
      saveLayoutState(layout, droppedWidgets);
    }, saveInterval);

    return () => clearTimeout(timeoutId);
  }, [layout, droppedWidgets, saveLayoutState, enablePersistence, saveInterval]);
}

/**
 * Utility function to serialize widgets for storage
 * Removes non-serializable properties like React components
 */
export function serializeWidgetsForStorage(widgets: Widget[]): Omit<Widget, 'component'>[] {
  return widgets.map(widget => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { component, ...serializableWidget } = widget;
    return serializableWidget;
  });
}

/**
 * Utility function to restore widgets from storage
 * Requires a widget registry to restore React components
 */
export function restoreWidgetsFromStorage(
  serializedWidgets: Omit<Widget, 'component'>[],
  widgetRegistry: Record<string, React.ComponentType<Record<string, unknown>>>
): Widget[] {
  return serializedWidgets
    .map(serializedWidget => {
      // Extract the base widget type from the instance ID
      // e.g., "counter-1234567890" -> "counter"
      const baseWidgetId = serializedWidget.id.split('-')[0];
      const component = widgetRegistry[baseWidgetId];

      if (!component) {
        console.warn(`Widget component not found in registry for widget ID: ${serializedWidget.id} (base: ${baseWidgetId})`);
        return null;
      }

      return {
        ...serializedWidget,
        component,
      } as Widget;
    })
    .filter((widget): widget is Widget => widget !== null);
}

/**
 * Enhanced layout persistence hook with widget serialization
 */
export function useLayoutPersistenceWithWidgets(
  widgetRegistry: Record<string, React.ComponentType<Record<string, unknown>>>,
  options: {
    enablePersistence?: boolean;
    storageKey?: string;
    autoSave?: boolean;
  } = {}
): UseLayoutPersistenceReturn & {
  saveLayoutStateWithWidgets: (layout: RGLLayout[], droppedWidgets: Widget[]) => void;
} {
  const {
    enablePersistence = true,
    storageKey = 'layout-state-with-widgets',
  } = options;

  // Store serialized layout state
  const [layoutState, setLayoutState, clearLayoutState] = useLocalStorage<{
    layout: RGLLayout[];
    droppedWidgets: Omit<Widget, 'component'>[];
    timestamp: number;
  } | null>(
    storageKey,
    null
  );

  // Check for persisted data using useMemo to prevent infinite loops
  const hasPersistedData = useMemo(() => {
    return layoutState !== null &&
      layoutState?.layout &&
      Array.isArray(layoutState.layout) &&
      layoutState.layout.length > 0;
  }, [layoutState]);

  // Save layout state with widget serialization
  const saveLayoutStateWithWidgets = useCallback(
    (layout: RGLLayout[], droppedWidgets: Widget[]) => {
      if (!enablePersistence) {
        return;
      }

      const serializedWidgets = serializeWidgetsForStorage(droppedWidgets);
      const stateData = {
        layout,
        droppedWidgets: serializedWidgets,
        timestamp: Date.now(),
      };

      setLayoutState(stateData);
    },
    [enablePersistence, setLayoutState]
  );

  // Regular save function (for compatibility)
  const saveLayoutState = useCallback(
    (layout: RGLLayout[], droppedWidgets: Widget[]) => {
      saveLayoutStateWithWidgets(layout, droppedWidgets);
    },
    [saveLayoutStateWithWidgets]
  );

  // Clear layout state
  const clearLayout = useCallback(() => {
    clearLayoutState();
  }, [clearLayoutState]);

  // Restore widgets from storage
  const savedLayout = layoutState?.layout || [];
  const savedDroppedWidgets = layoutState?.droppedWidgets
    ? restoreWidgetsFromStorage(layoutState.droppedWidgets, widgetRegistry)
    : [];

  return {
    savedLayout,
    savedDroppedWidgets,
    saveLayoutState,
    saveLayoutStateWithWidgets,
    clearLayoutState: clearLayout,
    hasPersistedData,
  };
}
