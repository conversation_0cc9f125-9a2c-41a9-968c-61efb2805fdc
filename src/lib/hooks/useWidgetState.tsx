import React, { useCallback, useEffect } from 'react';
import { useLocalStorage } from './useLocalStorage';
import type { UseWidgetStateReturn } from '../types';

/**
 * Hook for managing widget state with localStorage persistence
 * This hook provides state management for individual widgets with automatic persistence
 */
export function useWidgetState(
  widgetId: string,
  initialState: Record<string, unknown> = {},
  options: {
    enablePersistence?: boolean;
    storageKey?: string;
    autoSave?: boolean;
  } = {}
): UseWidgetStateReturn {
  const {
    enablePersistence = true,
    storageKey = 'widget-states',
  } = options;

  // Get all widget states from localStorage only if persistence is enabled
  const [allWidgetStates, setAllWidgetStates] = useLocalStorage<Record<string, Record<string, unknown>>>(
    enablePersistence ? storageKey : `disabled-${storageKey}`, // Use different key when disabled to avoid reading
    {}
  );

  // Initialize state only once when the widget is first created
  const [isInitialized, setIsInitialized] = React.useState(false);

  // Get the current widget's state
  const currentState = enablePersistence ? (allWidgetStates[widgetId] || initialState) : initialState;

  // Set the entire state for this widget
  const setState = useCallback(
    (newState: Record<string, unknown>) => {
      if (!enablePersistence) {
        return;
      }

      setAllWidgetStates(prev => ({
        ...prev,
        [widgetId]: newState,
      }));
    },
    [widgetId, enablePersistence, setAllWidgetStates]
  );

  // Update specific properties in the widget state
  const updateState = useCallback(
    (updates: Partial<Record<string, unknown>>) => {
      if (!enablePersistence) {
        return;
      }

      setAllWidgetStates(prev => ({
        ...prev,
        [widgetId]: {
          ...prev[widgetId],
          ...updates,
        },
      }));
    },
    [widgetId, enablePersistence, setAllWidgetStates]
  );

  // Clear the state for this widget
  const clearState = useCallback(() => {
    if (!enablePersistence) {
      return;
    }

    setAllWidgetStates(prev => {
      const newStates = { ...prev };
      delete newStates[widgetId];
      return newStates;
    });
  }, [widgetId, enablePersistence, setAllWidgetStates]);

  // Initialize state only once if it doesn't exist and we have initial state
  useEffect(() => {
    if (!isInitialized) {
      // Only initialize localStorage if persistence is enabled
      if (enablePersistence && !allWidgetStates[widgetId] && Object.keys(initialState).length > 0) {
        setAllWidgetStates(prev => ({
          ...prev,
          [widgetId]: initialState,
        }));
      }
      setIsInitialized(true);
    }
  }, [widgetId, enablePersistence, isInitialized, allWidgetStates, initialState, setAllWidgetStates]);

  return {
    state: currentState,
    setState,
    updateState,
    clearState,
  };
}

/**
 * Hook for managing global widget states (for use in WidgetsProvider)
 */
export function useGlobalWidgetStates(
  storageKey: string = 'widget-states',
  enablePersistence: boolean = true
) {
  const [allWidgetStates, setAllWidgetStates, clearAllStates] = useLocalStorage<Record<string, Record<string, unknown>>>(
    storageKey,
    {}
  );

  const updateWidgetState = useCallback(
    (widgetId: string, state: Record<string, unknown>) => {
      if (!enablePersistence) {
        return;
      }

      setAllWidgetStates(prev => ({
        ...prev,
        [widgetId]: state,
      }));
    },
    [enablePersistence, setAllWidgetStates]
  );

  const getWidgetState = useCallback(
    (widgetId: string): Record<string, unknown> | undefined => {
      return allWidgetStates[widgetId];
    },
    [allWidgetStates]
  );

  const clearWidgetState = useCallback(
    (widgetId: string) => {
      if (!enablePersistence) {
        return;
      }

      setAllWidgetStates(prev => {
        const newStates = { ...prev };
        delete newStates[widgetId];
        return newStates;
      });
    },
    [enablePersistence, setAllWidgetStates]
  );

  return {
    allWidgetStates,
    updateWidgetState,
    getWidgetState,
    clearWidgetState,
    clearAllStates,
  };
}

/**
 * Utility function to create a state-aware widget component
 * This higher-order function wraps a widget component to automatically manage its state
 */
export function withWidgetState<P extends Record<string, unknown>>(
  WrappedComponent: React.ComponentType<P>,
  widgetId: string,
  initialState: Record<string, unknown> = {}
) {
  return function StateAwareWidget(props: P) {
    const { state, setState, updateState, clearState } = useWidgetState(widgetId, initialState);

    const enhancedProps = {
      ...props,
      widgetState: state,
      setWidgetState: setState,
      updateWidgetState: updateState,
      clearWidgetState: clearState,
    } as P & {
      widgetState: Record<string, unknown>;
      setWidgetState: (state: Record<string, unknown>) => void;
      updateWidgetState: (updates: Partial<Record<string, unknown>>) => void;
      clearWidgetState: () => void;
    };

    return <WrappedComponent {...enhancedProps} />;
  };
}
