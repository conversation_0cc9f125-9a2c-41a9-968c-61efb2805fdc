import React, { useState, useEffect } from 'react';

interface CounterWidgetProps {
  initialValue?: number;
  step?: number;
  widgetId?: string;
  enablePersistence?: boolean;
}

export const CounterWidget: React.FC<CounterWidgetProps> = ({
  initialValue = 0,
  step = 1,
  widgetId = 'counter',
  enablePersistence = true,
}) => {
  // Simple state management
  const [count, setCount] = useState(initialValue);

  // Load from localStorage on mount if persistence is enabled
  useEffect(() => {
    if (enablePersistence && widgetId) {
      const saved = localStorage.getItem(`widget-${widgetId}`);
      if (saved) {
        try {
          const data = JSON.parse(saved);
          setCount(data.count || initialValue);
        } catch (error) {
          console.warn('Failed to load widget state:', error);
        }
      }
    }
  }, [widgetId, enablePersistence, initialValue]);

  // Save to localStorage when count changes (if persistence enabled)
  useEffect(() => {
    if (enablePersistence && widgetId) {
      localStorage.setItem(`widget-${widgetId}`, JSON.stringify({ count }));
    }
  }, [count, widgetId, enablePersistence]);

  const updateCount = (newCount: number) => {
    setCount(newCount);
  };

  return (
    <div style={{
      padding: '20px',
      border: '2px solid #007bff',
      borderRadius: '8px',
      textAlign: 'center',
      backgroundColor: '#f8f9fa'
    }}>
      <h3>Counter Widget</h3>
      <div style={{
        fontSize: '32px',
        margin: '15px 0',
        fontWeight: 'bold',
        color: '#fff',
        border: '3px solid #007bff',
        padding: '15px',
        borderRadius: '8px',
        backgroundColor: '#007bff',
        textAlign: 'center',
        minHeight: '60px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        COUNT: {count}
      </div>
      <div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            updateCount(count - step);
          }}
          style={{
            margin: '0 5px',
            padding: '8px 16px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          -
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            updateCount(count + step);
          }}
          style={{
            margin: '0 5px',
            padding: '8px 16px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          +
        </button>
      </div>
    </div>
  );
};
