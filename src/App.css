#root {
  padding: 1rem;
  text-align: center;
  width: 100%;
}

/* Widget catalog */
.widgets-catalog {
  background-color: #fff;
}

.widgets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.widget-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
  user-select: none;
}

.widget-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Grid layout */
.react-grid-item {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 2px solid #e9ecef;
  background-color: #fff;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
  cursor: move;
}

.react-grid-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: #3498db;
}

.react-grid-item.react-draggable-dragging {
  transform: rotate(3deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
  border-color: #2980b9;
  transition: none;
  opacity: 0.8;
}

.react-grid-item .widget-body {
  pointer-events: auto;
  cursor: default;
}

.react-grid-item .widget-header {
  pointer-events: auto;
  cursor: move;
}

.react-grid-item.react-grid-placeholder {
  background: linear-gradient(
    135deg,
    rgba(52, 152, 219, 0.1) 0%,
    rgba(52, 152, 219, 0.2) 100%
  );
  border: 3px solid #3498db;
  border-radius: 16px;
  animation: pulse-placeholder 1.5s ease-in-out infinite;
}

@keyframes pulse-placeholder {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* Grid Widget Container */
.grid-widget-container {
  height: 100%;
  width: 100%;
  position: relative;
}

.grid-widget-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 14px;
  overflow: hidden;
  position: relative;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  cursor: move;
  user-select: none;
}

.widget-header.drag-handle {
  cursor: move;
}

.widget-header:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  flex: 1;
}

.widget-icon {
  font-size: 16px;
  opacity: 0.9;
}

.widget-name {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.drag-indicator {
  font-size: 12px;
  opacity: 0.7;
  margin-left: 8px;
  letter-spacing: -2px;
  transition: opacity 0.2s ease;
}

.widget-header:hover .drag-indicator {
  opacity: 1;
}

.widget-actions {
  display: flex;
  align-items: center; 
  justify-content: flex-end;
  pointer-events: auto;
  position: relative;
  padding: 5px;
}

.widget-action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  pointer-events: auto;
}

.widget-body {
  flex: 1;
  padding: 16px;
  overflow: auto;
  background: #fff;
  position: relative;
  cursor: default;
  pointer-events: auto;
}

.widget-body * {
  pointer-events: auto !important;
}

.widget-body button {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 10;
  position: relative;
}

/* React Grid Layout Resize Handle Styles */
.react-resizable-handle {
  position: absolute !important;
  width: 20px !important;
  height: 20px !important;
  bottom: 0 !important;
  right: 0 !important;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  border-radius: 16px 0 14px 0 !important;
  cursor: se-resize !important;
  z-index: 1000 !important;
  opacity: 0.8 !important;
  transition: all 0.2s ease !important;
  pointer-events: auto !important;
}

.react-resizable-handle:hover {
  opacity: 1 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4) !important;
}

.react-resizable-handle::after {
  content: "⋱" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  pointer-events: none !important;
}

/* Ensure resize handles are visible on grid items */
.react-grid-item .react-resizable-handle {
  display: block !important;
  visibility: visible !important;
}

.widget-body:hover {
  background: #fafbfc;
}

/* Layout Container */
.layout-container {
  height: 100%;
  width: 100%;
  min-height: 760px;
  display: flex;
  flex-direction: column;
}

/* Grid Layout Container */
.layout-container .layout-grid {
  height: 100%;
  width: 100%;
  min-height: 760px;
  display: flex;
  flex-direction: column;
}

/* React Grid Layout Container */
.layout-container .react-grid-layout {
  height: 100% !important;
  min-height: 760px !important;
  width: 100% !important;
  flex: 1;
  position: relative;
}

/* Empty grid layout styling */
.layout-container .react-grid-layout:empty::before {
  content: "🎯 Drop widgets here to start building your layout";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  color: #6c757d;
  text-align: center;
  pointer-events: none;
  z-index: 1;
}

/* Grid layout with items */
.layout-container .react-grid-layout:not(:empty)::before {
  display: none;
}

/* Ensure all layout children take full height */
.layout-container > div,
.layout-grid,
.layout-grid > div {
  height: 100%;
  min-height: 760px;
}

/* Responsive improvements */
@media (max-width: 1200px) {
  .react-grid-item {
    font-size: 14px;
  }

  .widget-header {
    padding: 10px 14px;
  }

  .widget-title {
    font-size: 13px;
  }

  .widget-body {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .react-grid-item {
    font-size: 12px;
    padding: 8px;
  }

  .widget-header {
    padding: 8px 12px;
  }

  .widget-title {
    font-size: 12px;
  }

  .widget-icon {
    font-size: 14px;
  }

  .widget-body {
    padding: 10px;
  }

  .widget-action-btn {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
