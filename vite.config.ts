import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  if (mode === 'lib') {
    // Library build configuration
    return {
      plugins: [react()],
      build: {
        lib: {
          entry: resolve(__dirname, 'src/index.ts'),
          name: 'WidgetPackage',
          formats: ['es'],
          fileName: 'index',
        },
        rollupOptions: {
          external: [
            'react',
            'react-dom',
            'react-grid-layout',
            'react-resizable',
            'flexlayout-react'
          ],
          output: {
            globals: {
              react: 'React',
              'react-dom': 'ReactDOM',
              'react-grid-layout': 'ReactGridLayout',
              'react-resizable': 'ReactResizable',
              'flexlayout-react': 'FlexLayout',
            },
          },
        },
        sourcemap: true,
        emptyOutDir: true,
      },
    }
  }

  // Development configuration
  return {
    plugins: [react()],
  }
})
