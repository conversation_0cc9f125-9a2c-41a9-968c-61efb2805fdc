{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "moduleDetection": "force", "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationMap": true, "outDir": "dist", "emitDeclarationOnly": true}, "include": ["src/lib", "src/index.ts"], "exclude": ["src/main.tsx", "src/App.tsx", "src/example-widgets", "**/*.test.*", "**/*.spec.*"]}